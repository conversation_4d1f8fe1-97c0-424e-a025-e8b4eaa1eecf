<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { ref } from 'vue'

// 访问配置名
const configName = ref('toolSecurity')
// 访问服务名
const serviceName = ref('af-revenue')

const formGroupAddConstruction = ref(null)
</script>

<template>
  <div class="main">
    <!-- 调用XCellList组件渲染列表 -->
    <XCellList
      ref="formGroupAddConstruction"
      mode="新增"
      :config-name="configName"
      :service-name="serviceName"
    />
  </div>
</template>

<style scoped lang="less">
.main {
  padding: var(--base-interval-1);

  :deep(.van-search) {
    padding: 0;
  }
}
</style>
